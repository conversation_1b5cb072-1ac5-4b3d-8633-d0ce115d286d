[tool.pylance]
include = [
    "model-service/src",
    "ingest-service/src"
]

[tool.pyright]
include = [
    "model-service/src",
    "ingest-service/src"
]
pythonVersion = "3.11"
pythonPlatform = "Windows"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
