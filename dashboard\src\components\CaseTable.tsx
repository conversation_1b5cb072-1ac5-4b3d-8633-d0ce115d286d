import React, { useState } from 'react';
import '../styles/CaseTable.css';

const CaseTable = ({ cases, onSelectCase, isLoading }) => {
  const [sortConfig, setSortConfig] = useState({ key: 'created_at', direction: 'desc' });
  
  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };
  
  const sortedCases = [...cases].sort((a, b) => {
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === 'asc' ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });
  
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  const getStatusClass = (status) => {
    switch (status) {
      case 'open': return 'status-open';
      case 'closed': return 'status-closed';
      case 'pending': return 'status-pending';
      default: return '';
    }
  };
  
  const getTagClass = (tag) => {
    switch (tag) {
      case 'CONFIRMED': return 'tag-confirmed';
      case 'FP': return 'tag-fp';
      case 'SUSPICIOUS': return 'tag-suspicious';
      case 'NEEDS_REVIEW': return 'tag-needs-review';
      default: return '';
    }
  };

  return (
    <div className="case-table-wrapper">
      {isLoading ? (
        <div className="loading-indicator">Loading cases...</div>
      ) : (
        <>
          <table className="case-table">
            <thead>
              <tr>
                <th onClick={() => handleSort('id')}>
                  ID {sortConfig.key === 'id' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('transaction_id')}>
                  Transaction ID {sortConfig.key === 'transaction_id' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('tag')}>
                  Tag {sortConfig.key === 'tag' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('status')}>
                  Status {sortConfig.key === 'status' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('created_at')}>
                  Created {sortConfig.key === 'created_at' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th onClick={() => handleSort('updated_at')}>
                  Updated {sortConfig.key === 'updated_at' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedCases.map(caseItem => (
                <tr key={caseItem.id}>
                  <td>{caseItem.id}</td>
                  <td>{caseItem.transaction_id}</td>
                  <td>
                    <span className={`tag-badge ${getTagClass(caseItem.tag)}`}>
                      {caseItem.tag}
                    </span>
                  </td>
                  <td>
                    <span className={`status-badge ${getStatusClass(caseItem.status)}`}>
                      {caseItem.status}
                    </span>
                  </td>
                  <td>{formatDate(caseItem.created_at)}</td>
                  <td>{formatDate(caseItem.updated_at)}</td>
                  <td>
                    <button 
                      className="view-button"
                      onClick={() => onSelectCase(caseItem)}
                    >
                      View
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {sortedCases.length === 0 && !isLoading && (
            <div className="no-data">No cases found</div>
          )}
        </>
      )}
    </div>
  );
};

export default CaseTable;
