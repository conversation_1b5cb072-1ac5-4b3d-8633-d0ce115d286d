"""
FastAPI application for fraud detection model service.
"""
import os
import time
from datetime import datetime
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import prometheus_client
from prometheus_client import Counter, Histogram, generate_latest

from app.model import ModelWrapper
from app.enhanced_model import EnhancedFraudModel
from app.schemas import TransactionRequest, RiskScoreResponse, RiskScore, HealthResponse, MetricsResponse

# Initialize FastAPI app
app = FastAPI(
    title="Fraud Detection API",
    description="API for fraud detection model",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict to specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize metrics
REQUESTS = Counter('fraud_detection_requests_total', 'Total number of requests')
ERRORS = Counter('fraud_detection_errors_total', 'Total number of errors')
PROCESSING_TIME = Histogram('fraud_detection_processing_time_seconds', 'Time spent processing request')

# Load model - Enhanced model with fallback to original
MODEL_PATH = os.getenv("MODEL_PATH", "/app/models/fraud_detection_model.pkl")
if not os.path.exists(MODEL_PATH):
    # For local development, use relative path
    MODEL_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                             "models", "fraud_detection_model.pkl")

# Try enhanced model first, fallback to original if needed
USE_ENHANCED_MODEL = os.getenv("USE_ENHANCED_MODEL", "true").lower() == "true"

if USE_ENHANCED_MODEL:
    model = EnhancedFraudModel(MODEL_PATH, use_real_model=True)
else:
    model = ModelWrapper(MODEL_PATH)

@app.get("/health", response_model=HealthResponse)
async def health_check() -> Dict[str, Any]:
    """
    Health check endpoint.

    Returns:
        Health status information
    """
    return {
        "status": "ok",
        "version": "1.0.0",
        "timestamp": datetime.now()
    }

@app.get("/metrics")
async def metrics():
    """
    Prometheus metrics endpoint.

    Returns:
        Prometheus metrics in text format
    """
    return generate_latest()

@app.get("/model/info")
async def model_info() -> Dict[str, Any]:
    """
    Get information about the current model.

    Returns:
        Model information including type and features
    """
    if isinstance(model, EnhancedFraudModel):
        return model.get_model_info()
    else:
        return {
            "model_type": "legacy",
            "features": ["step", "type", "amount", "oldbalanceOrg", "newbalanceOrig", "oldbalanceDest", "newbalanceDest"],
            "model_loaded": True
        }

@app.post("/score", response_model=RiskScoreResponse)
async def score_transactions(request: TransactionRequest) -> Dict[str, Any]:
    """
    Score transactions for fraud risk.

    Args:
        request: Transaction data to score

    Returns:
        Risk scores for each transaction
    """
    REQUESTS.inc()
    start_time = time.time()

    try:
        # Convert Pydantic models to dictionaries
        transactions = [tx.dict() for tx in request.transactions]

        # Get predictions from model
        if isinstance(model, EnhancedFraudModel):
            predictions = model.predict_fraud_probability(transactions)
        else:
            predictions = model.predict_proba(transactions)

        # Format response
        results = [
            RiskScore(transaction_id=str(pred["transaction_id"]), risk=pred["risk"])
            for pred in predictions
        ]

        processing_time = time.time() - start_time
        PROCESSING_TIME.observe(processing_time)

        return {"results": results}

    except Exception as e:
        ERRORS.inc()
        raise HTTPException(status_code=500, detail=f"Error scoring transactions: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
