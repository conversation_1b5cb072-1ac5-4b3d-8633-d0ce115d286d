# VSCode Configuration for Fraud Detection Platform

This directory contains VSCode workspace configuration files to optimize the development experience for the fraud detection platform.

## Files

### `settings.json`
Main workspace settings that configure:
- **Python Interpreter**: Points to the correct Anaconda environment (`C:\Users\<USER>\anaconda3\envs\env\python.exe`)
- **Pylance Configuration**: Suppresses false positive diagnostics from built-in type stubs
- **Code Analysis**: Configures paths and exclusions for proper import resolution
- **Linting & Formatting**: Sets up flake8 and black for code quality

### `launch.json`
Debug configurations for:
- **Model Service**: Debug the FastAPI fraud detection model service
- **Ingest Service**: Debug the transaction ingestion service

### `extensions.json`
Recommended VSCode extensions for Python development:
- Python extension pack
- Pylance for type checking
- Black formatter
- Flake8 linter

## Key Configuration Features

### Diagnostic Suppression
The configuration suppresses false positive errors from Pylance's built-in type stubs:
- `reportIncompatibleMethodOverride`: Suppresses method override warnings in built-in types
- `reportOverlappingOverload`: Suppresses overload conflicts in type stubs
- Various optional type warnings that don't apply to this project

### Path Configuration
- **Extra Paths**: Adds service source directories to Python path
- **Include Paths**: Focuses analysis on project code
- **Exclude Paths**: Ignores extension files and type stubs that cause false positives

### Performance Optimization
- Limited indexing depth for faster analysis
- Workspace-only diagnostic mode
- Excluded file watchers for irrelevant directories

## Troubleshooting

If you see import errors:
1. Reload VSCode window: `Ctrl+Shift+P` → "Developer: Reload Window"
2. Select correct interpreter: `Ctrl+Shift+P` → "Python: Select Interpreter"
3. Verify the interpreter path in settings matches your environment

If you see type checking errors in built-in Python files:
- These should be suppressed by the configuration
- Check that `pyrightconfig.json` is being used
- Verify diagnostic overrides are properly set
