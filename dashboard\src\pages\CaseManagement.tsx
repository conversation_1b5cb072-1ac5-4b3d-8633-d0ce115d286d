import React, { useState, useEffect } from 'react';
import { fetchCases, createCase, updateCase } from '../services/api';
import CaseTable from '../components/CaseTable';
import CaseForm from '../components/CaseForm';
import '../styles/CaseManagement.css';

const CaseManagement = () => {
  const [cases, setCases] = useState([]);
  const [selectedCase, setSelectedCase] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState({ status: 'all' });
  const [isFormOpen, setIsFormOpen] = useState(false);

  useEffect(() => {
    loadCases();
  }, [filter]);

  const loadCases = async () => {
    setIsLoading(true);
    try {
      const data = await fetchCases(filter);
      setCases(data);
    } catch (error) {
      console.error('Error fetching cases:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCaseSelect = (caseItem) => {
    setSelectedCase(caseItem);
    setIsFormOpen(true);
  };

  const handleFilterChange = (e) => {
    setFilter({
      ...filter,
      [e.target.name]: e.target.value
    });
  };

  const handleCreateCase = async (caseData) => {
    try {
      await createCase(caseData);
      loadCases();
      setIsFormOpen(false);
    } catch (error) {
      console.error('Error creating case:', error);
    }
  };

  const handleUpdateCase = async (caseId, caseData) => {
    try {
      await updateCase(caseId, caseData);
      loadCases();
      setIsFormOpen(false);
      setSelectedCase(null);
    } catch (error) {
      console.error('Error updating case:', error);
    }
  };

  const handleNewCase = () => {
    setSelectedCase(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedCase(null);
  };

  return (
    <div className="case-management-container">
      <div className="case-management-header">
        <h1>Case Management</h1>
        <div className="case-management-actions">
          <button className="primary-button" onClick={handleNewCase}>
            New Case
          </button>
          <div className="filter-controls">
            <label htmlFor="statusFilter">Status:</label>
            <select 
              id="statusFilter" 
              name="status" 
              value={filter.status} 
              onChange={handleFilterChange}
            >
              <option value="all">All</option>
              <option value="open">Open</option>
              <option value="closed">Closed</option>
              <option value="pending">Pending</option>
            </select>
          </div>
        </div>
      </div>

      <div className="case-management-content">
        <div className="card">
          <CaseTable 
            cases={cases} 
            onSelectCase={handleCaseSelect}
            isLoading={isLoading}
          />
        </div>
      </div>

      {isFormOpen && (
        <div className="case-form-modal">
          <div className="case-form-container">
            <button className="close-button" onClick={handleCloseForm}>×</button>
            <CaseForm 
              initialCase={selectedCase}
              onSubmit={selectedCase ? 
                (data) => handleUpdateCase(selectedCase.id, data) : 
                handleCreateCase
              }
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default CaseManagement;
