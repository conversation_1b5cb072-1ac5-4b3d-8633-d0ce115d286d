/* Main application styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --secondary-color: #64748b;
  --danger-color: #ef4444;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --text-color: #1e293b;
  --text-light: #64748b;
  --bg-color: #f8fafc;
  --card-bg: #ffffff;
  --border-color: #e2e8f0;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.5;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.content {
  flex: 1;
  padding: 1.5rem;
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: 1.875rem;
}

h2 {
  font-size: 1.5rem;
}

h3 {
  font-size: 1.25rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
  font-family: inherit;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: none;
  background-color: var(--primary-color);
  color: white;
  font-weight: 500;
  transition: background-color 0.2s;
}

button:hover {
  background-color: var(--primary-hover);
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

input, select, textarea {
  font-family: inherit;
  font-size: 1rem;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  width: 100%;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.card {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.125rem;
  color: var(--text-light);
}

.error-message {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.success-message {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.risk-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.high-risk {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.medium-risk {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.low-risk {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-open {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.status-closed {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.tag-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.tag-confirmed {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.tag-fp {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.tag-suspicious {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.tag-needs-review {
  background-color: rgba(100, 116, 139, 0.1);
  color: var(--secondary-color);
}

/* Responsive utilities */
@media (min-width: 768px) {
  .app-container {
    flex-direction: row;
  }
  
  .content {
    padding: 2rem;
  }
}
