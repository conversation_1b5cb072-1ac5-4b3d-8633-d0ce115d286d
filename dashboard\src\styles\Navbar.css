/* Navbar styles */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 1rem 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navbar-brand h1 {
  font-size: 1.25rem;
  margin: 0;
  color: var(--primary-color);
}

.navbar-menu {
  display: flex;
  gap: 1.5rem;
}

.navbar-item {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
}

.navbar-item:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.navbar-item.active {
  color: var(--primary-color);
}

.navbar-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--primary-color);
}

.navbar-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.username {
  font-weight: 500;
}

.role {
  font-size: 0.75rem;
  color: var(--text-light);
}

.logout-button {
  background-color: transparent;
  color: var(--text-light);
  border: 1px solid var(--border-color);
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.logout-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    padding: 1rem;
  }
  
  .navbar-brand {
    margin-bottom: 1rem;
  }
  
  .navbar-menu {
    width: 100%;
    justify-content: space-between;
    margin-bottom: 1rem;
  }
  
  .navbar-user {
    width: 100%;
    justify-content: space-between;
  }
}
