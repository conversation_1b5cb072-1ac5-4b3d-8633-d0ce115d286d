"""
Pydantic schemas for the ingest service.
"""
from pydantic import BaseModel, Field, validator
from typing import List, Optional
from enum import Enum
import datetime

class TransactionType(str, Enum):
    """Enum for transaction types"""
    PAYMENT = "PAYMENT"
    TRANSFER = "TRANSFER"
    CASH_OUT = "CASH_OUT"
    DEBIT = "DEBIT"

class TransactionBase(BaseModel):
    """Base schema for transaction data"""
    transaction_id: Optional[str] = None
    step: int = Field(..., ge=1)
    type: TransactionType
    amount: float = Field(..., gt=0)
    nameOrig: str
    oldbalanceOrg: float = Field(..., ge=0)
    newbalanceOrig: float = Field(..., ge=0)
    nameDest: str
    oldbalanceDest: float = Field(..., ge=0)
    newbalanceDest: float = Field(..., ge=0)
    
    @validator('newbalanceOrig')
    def validate_balance_orig(cls, v, values):
        """Validate that newbalanceOrig is less than or equal to oldbalanceOrg"""
        if 'oldbalanceOrg' in values and v > values['oldbalanceOrg']:
            raise ValueError('newbalanceOrig must be less than or equal to oldbalanceOrg')
        return v
    
    @validator('transaction_id', pre=True, always=True)
    def set_transaction_id(cls, v, values):
        """Set transaction_id if not provided"""
        if v is None and 'nameOrig' in values:
            return values['nameOrig']
        return v

class KafkaMessage(BaseModel):
    """Schema for Kafka message"""
    transaction: TransactionBase
    timestamp: datetime.datetime = Field(default_factory=datetime.datetime.now)

class EnrichedTransaction(TransactionBase):
    """Schema for enriched transaction with additional features"""
    merchantFlag: int = 0
    type_CASH_OUT: int = 0
    type_DEBIT: int = 0
    type_PAYMENT: int = 0
    type_TRANSFER: int = 0
    balanceDiffOrig: float = 0
    balanceDiffDest: float = 0

class ModelServiceRequest(BaseModel):
    """Schema for model service request"""
    transactions: List[EnrichedTransaction]

class RiskScore(BaseModel):
    """Schema for risk score response"""
    transaction_id: str
    risk: float = Field(..., ge=0, le=1)

class ModelServiceResponse(BaseModel):
    """Schema for model service response"""
    results: List[RiskScore]

class HealthResponse(BaseModel):
    """Schema for health check response"""
    status: str
    version: str
    timestamp: datetime.datetime
