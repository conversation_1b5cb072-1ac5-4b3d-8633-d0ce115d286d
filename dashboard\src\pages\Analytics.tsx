import React, { useState, useEffect } from 'react';
import { Line, Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { fetchCaseStats, fetchFeatureImportance, fetchFraudTrend } from '../services/api';
import '../styles/Analytics.css';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend);

const Analytics = () => {
  const [caseStats, setCaseStats] = useState(null);
  const [featureImportance, setFeatureImportance] = useState([]);
  const [fraudTrend, setFraudTrend] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState('week');

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        const [statsData, importanceData, trendData] = await Promise.all([
          fetchCaseStats(),
          fetchFeatureImportance(),
          fetchFraudTrend(dateRange)
        ]);
        
        setCaseStats(statsData);
        setFeatureImportance(importanceData);
        setFraudTrend(trendData);
      } catch (error) {
        console.error('Error loading analytics data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, [dateRange]);

  const handleDateRangeChange = (e) => {
    setDateRange(e.target.value);
  };

  // Prepare data for fraud trend chart
  const trendChartData = {
    labels: fraudTrend.map(item => item.date),
    datasets: [
      {
        label: 'Fraud Cases',
        data: fraudTrend.map(item => item.count),
        fill: false,
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgba(255, 99, 132, 1)',
        tension: 0.1
      }
    ]
  };

  // Prepare data for feature importance chart
  const importanceChartData = {
    labels: featureImportance.map(item => item.feature),
    datasets: [
      {
        label: 'Feature Importance',
        data: featureImportance.map(item => item.importance),
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      }
    ]
  };

  return (
    <div className="analytics-container">
      <div className="analytics-header">
        <h1>Analytics Dashboard</h1>
        <div className="date-range-selector">
          <label htmlFor="dateRange">Time Range:</label>
          <select id="dateRange" value={dateRange} onChange={handleDateRangeChange}>
            <option value="day">Last 24 Hours</option>
            <option value="week">Last 7 Days</option>
            <option value="month">Last 30 Days</option>
            <option value="year">Last Year</option>
          </select>
        </div>
      </div>

      {isLoading ? (
        <div className="loading">Loading analytics data...</div>
      ) : (
        <div className="analytics-content">
          <div className="analytics-row">
            <div className="card">
              <h2>Fraud Trend Over Time</h2>
              <div className="chart-container">
                <Line data={trendChartData} options={{ maintainAspectRatio: false }} />
              </div>
            </div>
          </div>

          <div className="analytics-row">
            <div className="card">
              <h2>Feature Importance</h2>
              <div className="chart-container">
                <Bar 
                  data={importanceChartData} 
                  options={{ 
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                  }} 
                />
              </div>
            </div>
          </div>

          {caseStats && (
            <div className="analytics-row">
              <div className="card">
                <h2>Case Statistics</h2>
                <div className="stats-grid">
                  <div className="stat-item">
                    <h3>Total Cases</h3>
                    <p>{caseStats.total_cases}</p>
                  </div>
                  <div className="stat-item">
                    <h3>Open Cases</h3>
                    <p>{caseStats.status_counts.open || 0}</p>
                  </div>
                  <div className="stat-item">
                    <h3>Closed Cases</h3>
                    <p>{caseStats.status_counts.closed || 0}</p>
                  </div>
                  <div className="stat-item">
                    <h3>Confirmed Fraud</h3>
                    <p>{caseStats.tag_counts.CONFIRMED || 0}</p>
                  </div>
                  <div className="stat-item">
                    <h3>False Positives</h3>
                    <p>{caseStats.tag_counts.FP || 0}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Analytics;
