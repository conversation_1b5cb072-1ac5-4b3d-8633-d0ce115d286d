{"include": ["model-service/src", "ingest-service/src", "dashboard/src", "*.py"], "exclude": ["**/node_modules", "**/__pycache__", "**/typeshed-fallback", "**/.vscode/extensions"], "ignore": ["**/typeshed-fallback/**", "**/.vscode/extensions/**"], "defineConstant": {"DEBUG": true}, "stubPath": "", "reportMissingImports": "information", "reportMissingTypeStubs": "none", "reportIncompatibleMethodOverride": "none", "reportIncompatibleVariableOverride": "none", "reportOverlappingOverload": "none", "reportGeneralTypeIssues": "none", "reportOptionalSubscript": "none", "reportOptionalMemberAccess": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none", "reportTypedDictNotRequiredAccess": "none", "reportUntypedFunctionDecorator": "none", "reportUntypedClassDecorator": "none", "reportUntypedBaseClass": "none", "reportUntypedNamedTuple": "none", "reportPrivateUsage": "none", "reportConstantRedefinition": "none", "reportMissingSuperCall": "none", "reportUninitializedInstanceVariable": "none", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnnecessaryCast": "none", "reportUnnecessaryComparison": "none", "reportUnnecessaryContains": "none", "reportAssertAlwaysTrue": "none", "reportSelfClsParameterName": "none", "reportImplicitStringConcatenation": "none", "reportUndefinedVariable": "error", "reportUnboundVariable": "error", "reportInvalidStringEscapeSequence": "error", "reportInvalidTypeVarUse": "error", "reportMissingParameterType": "none", "reportMissingTypeArgument": "none", "reportInvalidTypeForm": "error", "reportImportCycles": "none", "reportUnusedImport": "none", "reportUnusedClass": "none", "reportUnusedFunction": "none", "reportUnusedVariable": "none", "reportDuplicateImport": "none", "reportWildcardImportFromLibrary": "none", "reportAbstractUsage": "error", "reportArgumentType": "error", "reportAssignmentType": "error", "reportAttributeAccessIssue": "error", "reportCallIssue": "error", "reportInconsistentConstructor": "none", "reportInvalidStubStatement": "none", "reportIncompleteStub": "none", "reportUnsupportedDunderAll": "none", "reportUnusedCoroutine": "error", "pythonVersion": "3.11", "pythonPlatform": "Windows", "executionEnvironments": [{"root": "model-service/src", "pythonVersion": "3.11", "pythonPlatform": "Windows", "extraPaths": ["model-service/src"]}, {"root": "ingest-service/src", "pythonVersion": "3.11", "pythonPlatform": "Windows", "extraPaths": ["ingest-service/src"]}]}