/* TransactionTable styles */
.transaction-table-wrapper {
  width: 100%;
  overflow: hidden;
}

.transaction-table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1.5fr 0.5fr;
  background-color: #f8fafc;
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
}

.header-cell {
  padding: 0.75rem 1rem;
  cursor: pointer;
  user-select: none;
}

.header-cell:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.transaction-table-body {
  position: relative;
}

.transaction-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1.5fr 0.5fr;
  border-bottom: 1px solid var(--border-color);
}

.cell {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
}

.view-button {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.view-button:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

.no-data {
  padding: 2rem;
  text-align: center;
  color: var(--text-light);
}

.loading-indicator {
  padding: 2rem;
  text-align: center;
  color: var(--text-light);
}
