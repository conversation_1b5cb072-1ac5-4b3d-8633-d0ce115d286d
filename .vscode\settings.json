{"python.analysis.diagnosticSeverityOverrides": {"reportIncompatibleMethodOverride": "none", "reportMissingImports": "information", "reportIncompatibleVariableOverride": "none", "reportOverlappingOverload": "none", "reportGeneralTypeIssues": "none", "reportOptionalSubscript": "none", "reportOptionalMemberAccess": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none", "reportTypedDictNotRequiredAccess": "none"}, "python.analysis.typeCheckingMode": "basic", "python.defaultInterpreterPath": "C:\\Users\\<USER>\\anaconda3\\envs\\env\\python.exe", "python.pythonPath": "C:\\Users\\<USER>\\anaconda3\\envs\\env\\python.exe", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.analysis.autoImportCompletions": true, "python.analysis.completeFunctionParens": true, "python.analysis.extraPaths": ["./model-service/src", "./ingest-service/src"], "python.analysis.include": ["./model-service/src/**", "./ingest-service/src/**"], "python.analysis.exclude": ["**/typeshed-fallback/**", "**/.vscode/extensions/**", "**/site-packages/**"], "python.analysis.stubPath": "", "python.analysis.useLibraryCodeForTypes": false, "python.analysis.indexing": true, "python.analysis.packageIndexDepths": [{"name": "", "depth": 2, "includeAllSymbols": false}], "python.analysis.diagnosticMode": "workspace", "python.analysis.autoSearchPaths": true, "python.analysis.logLevel": "Warning", "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.vscode/extensions": true}, "files.watcherExclude": {"**/.vscode/extensions/**": true, "**/typeshed-fallback/**": true}}