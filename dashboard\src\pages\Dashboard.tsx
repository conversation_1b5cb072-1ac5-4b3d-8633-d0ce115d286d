import React, { useState, useEffect, useRef } from 'react';
import TransactionTable from '../components/TransactionTable';
import RiskHeatmap from '../components/RiskHeatmap';
import CaseDrawer from '../components/CaseDrawer';
import { useWebSocket } from '../services/WebSocketContext';
import { fetchLatestTransactions } from '../services/api';
import '../styles/Dashboard.css';

const Dashboard = () => {
  const [transactions, setTransactions] = useState([]);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { lastMessage } = useWebSocket();
  const transactionsRef = useRef([]);

  useEffect(() => {
    // Load initial transactions
    const loadTransactions = async () => {
      try {
        setIsLoading(true);
        const data = await fetchLatestTransactions(100);
        setTransactions(data);
        transactionsRef.current = data;
      } catch (error) {
        console.error('Error fetching transactions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTransactions();
  }, []);

  useEffect(() => {
    // Handle new transactions from WebSocket
    if (lastMessage) {
      try {
        const messageData = JSON.parse(lastMessage);
        if (messageData.transaction && messageData.risk_score !== undefined) {
          const newTransaction = {
            ...messageData.transaction,
            risk_score: messageData.risk_score,
            timestamp: messageData.timestamp
          };
          
          // Add to beginning of array and limit size
          const updatedTransactions = [newTransaction, ...transactionsRef.current].slice(0, 1000);
          setTransactions(updatedTransactions);
          transactionsRef.current = updatedTransactions;
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    }
  }, [lastMessage]);

  const handleTransactionSelect = (transaction) => {
    setSelectedTransaction(transaction);
    setIsDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
  };

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1>Fraud Detection Dashboard</h1>
        <div className="dashboard-stats">
          <div className="stat-card">
            <h3>Total Transactions</h3>
            <p>{transactions.length}</p>
          </div>
          <div className="stat-card">
            <h3>High Risk</h3>
            <p>{transactions.filter(t => t.risk_score >= 0.8).length}</p>
          </div>
          <div className="stat-card">
            <h3>Medium Risk</h3>
            <p>{transactions.filter(t => t.risk_score >= 0.5 && t.risk_score < 0.8).length}</p>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="dashboard-main">
          <div className="card transaction-table-card">
            <h2>Live Transaction Feed</h2>
            <TransactionTable 
              transactions={transactions} 
              onSelectTransaction={handleTransactionSelect}
              isLoading={isLoading}
            />
          </div>
        </div>
        
        <div className="dashboard-sidebar">
          <div className="card">
            <h2>Risk Heatmap</h2>
            <RiskHeatmap transactions={transactions} />
          </div>
        </div>
      </div>

      <CaseDrawer 
        isOpen={isDrawerOpen} 
        onClose={handleDrawerClose}
        transaction={selectedTransaction}
      />
    </div>
  );
};

export default Dashboard;
