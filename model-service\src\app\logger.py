"""
Logger configuration for the model service.
"""
import logging
import json
from typing import Dict, Any

class JSONFormatter(logging.Formatter):
    """
    Formatter for JSON-structured logs.
    """
    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record as JSON.

        Args:
            record: Log record to format

        Returns:
            JSON-formatted log string
        """
        log_data: Dict[str, Any] = {
            "timestamp": self.formatTime(record, self.datefmt),
            "level": record.levelname,
            "name": record.name,
            "message": record.getMessage(),
        }

        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # Add extra fields if present
        # Extra fields are added directly to the record's __dict__ when logging
        reserved_attrs = {
            'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
            'module', 'exc_info', 'exc_text', 'stack_info', 'lineno', 'funcName',
            'created', 'msecs', 'relativeCreated', 'thread', 'threadName',
            'processName', 'process', 'getMessage', 'extra'
        }

        for key, value in record.__dict__.items():
            if key not in reserved_attrs and not key.startswith('_'):
                log_data[key] = value

        return json.dumps(log_data)

def setup_logger(name: str = "fraud_detection") -> logging.Logger:
    """
    Set up and configure logger.

    Args:
        name: Logger name

    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # Create console handler
    handler = logging.StreamHandler()
    handler.setFormatter(JSONFormatter())

    # Add handler to logger
    logger.addHandler(handler)

    return logger

# Create default logger instance
logger = setup_logger()
