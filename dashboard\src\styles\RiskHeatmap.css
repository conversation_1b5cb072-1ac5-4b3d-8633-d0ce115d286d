/* RiskHeatmap styles */
.risk-heatmap-container {
  width: 100%;
}

.heatmap-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.view-button {
  background-color: var(--secondary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.view-button.active {
  background-color: var(--primary-color);
}

.heatmap-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.heatmap-chart {
  width: 100%;
}

.heatmap-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 0.25rem;
}

.heatmap-cell {
  border: 1px solid var(--border-color);
  border-radius: 0.25rem;
  padding: 0.5rem;
  text-align: center;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cell-label {
  font-size: 0.75rem;
  color: var(--text-light);
}

.cell-count {
  font-size: 1rem;
  font-weight: 600;
  margin: 0.25rem 0;
}

.risk-bars {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: auto;
}

.risk-bar {
  height: 4px;
  border-radius: 2px;
}

.risk-bar.high {
  background-color: var(--danger-color);
}

.risk-bar.medium {
  background-color: var(--warning-color);
}

.risk-bar.low {
  background-color: var(--success-color);
}

.risk-distribution {
  margin-top: 1rem;
}

.risk-distribution h3 {
  margin-bottom: 1rem;
  text-align: center;
}

.pie-chart-container {
  height: 200px;
  position: relative;
}

@media (min-width: 768px) {
  .heatmap-content {
    flex-direction: row;
  }
  
  .heatmap-chart {
    flex: 2;
  }
  
  .risk-distribution {
    flex: 1;
    margin-top: 0;
  }
}
