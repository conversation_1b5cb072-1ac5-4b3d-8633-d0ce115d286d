/* CaseDrawer styles */
.case-drawer {
  position: fixed;
  top: 0;
  right: -600px;
  width: 100%;
  max-width: 600px;
  height: 100vh;
  background-color: var(--card-bg);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.3s ease-in-out;
  overflow-y: auto;
}

.case-drawer.open {
  right: 0;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.drawer-header h2 {
  margin: 0;
}

.close-button {
  background: none;
  color: var(--text-color);
  font-size: 1.5rem;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.drawer-content {
  padding: 1.5rem;
}

.transaction-details {
  margin-bottom: 2rem;
}

.detail-group {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.detail-group:last-child {
  border-bottom: none;
}

.detail-group h3 {
  margin-bottom: 1rem;
  color: var(--text-light);
  font-size: 1rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.detail-label {
  font-weight: 500;
}

.case-form h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.submit-button {
  width: 100%;
  margin-top: 1rem;
}
