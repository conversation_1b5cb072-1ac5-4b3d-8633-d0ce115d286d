/* Dashboard page styles */
.dashboard-container {
  padding: 1rem;
}

.dashboard-header {
  margin-bottom: 1.5rem;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.stat-card {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: var(--shadow);
  text-align: center;
}

.stat-card h3 {
  font-size: 0.875rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.stat-card p {
  font-size: 1.5rem;
  font-weight: 600;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.transaction-table-card {
  overflow: hidden;
}

@media (min-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 2fr 1fr;
  }
}
