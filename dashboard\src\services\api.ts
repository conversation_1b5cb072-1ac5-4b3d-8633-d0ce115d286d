import axios from 'axios';

// API base URL
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Authentication API
export const loginUser = async (username, password) => {
  const formData = new FormData();
  formData.append('username', username);
  formData.append('password', password);
  
  const response = await api.post('/auth/login', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
  return response.data;
};

// Transaction API
export const fetchLatestTransactions = async (limit = 50) => {
  const response = await api.get(`/transactions/latest?n=${limit}`);
  return response.data;
};

// Case Management API
export const fetchCases = async (filter = {}) => {
  let url = '/cases';
  const params = new URLSearchParams();
  
  if (filter.status && filter.status !== 'all') {
    params.append('status', filter.status);
  }
  
  if (filter.transaction_id) {
    params.append('transaction_id', filter.transaction_id);
  }
  
  if (params.toString()) {
    url += `?${params.toString()}`;
  }
  
  const response = await api.get(url);
  return response.data;
};

export const fetchCase = async (id) => {
  const response = await api.get(`/cases/${id}`);
  return response.data;
};

export const createCase = async (caseData) => {
  const response = await api.post('/cases', caseData);
  return response.data;
};

export const updateCase = async (id, caseData) => {
  const response = await api.put(`/cases/${id}`, caseData);
  return response.data;
};

// Analytics API
export const fetchCaseStats = async () => {
  const response = await api.get('/cases/stats');
  return response.data;
};

export const fetchFeatureImportance = async () => {
  const response = await api.get('/feature-importance');
  return response.data || [
    { feature: 'type_TRANSFER', importance: 0.35 },
    { feature: 'amount', importance: 0.25 },
    { feature: 'oldbalanceOrg', importance: 0.15 },
    { feature: 'newbalanceOrig', importance: 0.10 },
    { feature: 'oldbalanceDest', importance: 0.08 },
    { feature: 'newbalanceDest', importance: 0.07 }
  ];
};

export const fetchFraudTrend = async (dateRange = 'week') => {
  const response = await api.get(`/fraud-trend?range=${dateRange}`);
  return response.data || generateMockTrendData(dateRange);
};

// Helper function to generate mock trend data for demo purposes
const generateMockTrendData = (dateRange) => {
  const now = new Date();
  const data = [];
  let days;
  
  switch (dateRange) {
    case 'day':
      days = 1;
      break;
    case 'week':
      days = 7;
      break;
    case 'month':
      days = 30;
      break;
    case 'year':
      days = 365;
      break;
    default:
      days = 7;
  }
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    data.push({
      date: date.toISOString().split('T')[0],
      count: Math.floor(Math.random() * 10) + 1
    });
  }
  
  return data;
};

export default api;
