"""
Utility functions for feature enrichment and preprocessing.
"""
import pandas as pd
import numpy as np
from typing import Dict, Any, List

def add_merchant_flag(transaction: Dict[str, Any]) -> Dict[str, Any]:
    """
    Add merchant flag feature to transaction.
    
    Args:
        transaction: Transaction data
        
    Returns:
        Transaction with merchant flag added
    """
    if 'nameDest' in transaction:
        transaction['merchantFlag'] = 1 if transaction['nameDest'].startswith('M') else 0
    else:
        transaction['merchantFlag'] = 0
    return transaction

def add_transaction_type_features(transaction: Dict[str, Any]) -> Dict[str, Any]:
    """
    Add one-hot encoded transaction type features.
    
    Args:
        transaction: Transaction data
        
    Returns:
        Transaction with type features added
    """
    transaction_types = ['CASH_OUT', 'DEBIT', 'PAYMENT', 'TRANSFER']
    for t_type in transaction_types:
        transaction[f'type_{t_type}'] = 1 if transaction.get('type') == t_type else 0
    return transaction

def add_balance_diff_features(transaction: Dict[str, Any]) -> Dict[str, Any]:
    """
    Add balance difference features.
    
    Args:
        transaction: Transaction data
        
    Returns:
        Transaction with balance difference features added
    """
    # Calculate origin balance difference
    if 'oldbalanceOrg' in transaction and 'newbalanceOrig' in transaction:
        transaction['balanceDiffOrig'] = transaction['oldbalanceOrg'] - transaction['newbalanceOrig']
    else:
        transaction['balanceDiffOrig'] = 0
        
    # Calculate destination balance difference
    if 'oldbalanceDest' in transaction and 'newbalanceDest' in transaction:
        transaction['balanceDiffDest'] = transaction['newbalanceDest'] - transaction['oldbalanceDest']
    else:
        transaction['balanceDiffDest'] = 0
        
    return transaction

def enrich_transaction(transaction: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enrich transaction with additional features.
    
    Args:
        transaction: Raw transaction data
        
    Returns:
        Enriched transaction with additional features
    """
    # Create a copy to avoid modifying the original
    enriched = transaction.copy()
    
    # Add features
    enriched = add_merchant_flag(enriched)
    enriched = add_transaction_type_features(enriched)
    enriched = add_balance_diff_features(enriched)
    
    return enriched

def enrich_transactions(transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Enrich multiple transactions with additional features.
    
    Args:
        transactions: List of raw transaction data
        
    Returns:
        List of enriched transactions
    """
    return [enrich_transaction(tx) for tx in transactions]
