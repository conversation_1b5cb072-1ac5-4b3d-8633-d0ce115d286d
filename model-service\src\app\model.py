"""
Model wrapper for fraud detection model.
"""
import os
import joblib
import pandas as pd
import numpy as np
from typing import Dict, List, Union, Any

class ModelWrapper:
    """
    Wrapper for the fraud detection model.
    Handles loading the model and preprocessing input data.
    """
    def __init__(self, model_path: str):
        """
        Initialize the model wrapper.

        Args:
            model_path: Path to the serialized model file
        """
        try:
            self.model = joblib.load(model_path)
        except Exception as e:
            print(f"Warning: Could not load model from {model_path}: {e}")
            print("Using mock model for demonstration purposes")
            self.model = None

        self.feature_columns = [
            'step', 'type', 'amount', 'oldbalanceOrg', 'newbalanceOrig',
            'oldbalanceDest', 'newbalanceDest'
        ]

    def _preprocess(self, transaction: Dict[str, Any]) -> pd.DataFrame:
        """
        Preprocess a transaction for prediction.

        Args:
            transaction: Dictionary containing transaction data

        Returns:
            DataFrame with preprocessed features
        """
        # Create a DataFrame with the transaction
        df = pd.DataFrame([transaction])

        # One-hot encode the transaction type
        transaction_types = ['CASH_OUT', 'DEBIT', 'PAYMENT', 'TRANSFER']
        for t_type in transaction_types:
            df[f'type_{t_type}'] = (df['type'] == t_type).astype(int)

        # Add merchant flag feature (if destination starts with 'M')
        if 'nameDest' in df.columns:
            df['merchantFlag'] = df['nameDest'].str.startswith('M').astype(int)
        else:
            df['merchantFlag'] = 0

        return df

    def _mock_predict(self, transaction: Dict[str, Any]) -> float:
        """
        Mock prediction function for demonstration purposes.

        Args:
            transaction: Dictionary containing transaction data

        Returns:
            Mock fraud probability between 0 and 1
        """
        import random

        # Simple heuristic-based mock model
        risk_score = 0.1  # Base risk

        # Higher risk for large amounts
        amount = transaction.get('amount', 0)
        if amount > 100000:
            risk_score += 0.4
        elif amount > 50000:
            risk_score += 0.2
        elif amount > 10000:
            risk_score += 0.1

        # Higher risk for certain transaction types
        tx_type = transaction.get('type', '')
        if tx_type in ['CASH_OUT', 'TRANSFER']:
            risk_score += 0.3

        # Add some randomness
        risk_score += random.uniform(-0.1, 0.1)

        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, risk_score))

    def predict_proba(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, float]]:
        """
        Predict fraud probability for a list of transactions.

        Args:
            transactions: List of transaction dictionaries

        Returns:
            List of dictionaries with transaction_id and risk score
        """
        results = []

        for transaction in transactions:
            # Preprocess the transaction
            df = self._preprocess(transaction)

            # Get the fraud probability (class 1)
            try:
                if self.model is not None:
                    fraud_prob = float(self.model.predict_proba(df)[:, 1][0])
                else:
                    # Mock model: generate risk based on transaction characteristics
                    fraud_prob = self._mock_predict(transaction)
            except Exception:
                # Fallback if model prediction fails
                fraud_prob = 0.0

            # Create result with transaction_id and risk score
            transaction_id = transaction.get("transaction_id")
            if transaction_id is None:
                transaction_id = transaction.get("nameOrig", "unknown")

            result = {
                "transaction_id": transaction_id,
                "risk": fraud_prob
            }
            results.append(result)

        return results

    def predict(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Make binary predictions for a list of transactions.

        Args:
            transactions: List of transaction dictionaries

        Returns:
            List of dictionaries with transaction_id and prediction (0 or 1)
        """
        results = []
        proba_results = self.predict_proba(transactions)

        for i, transaction in enumerate(transactions):
            # Get the probability result
            proba = proba_results[i]["risk"]

            # Apply threshold (0.5 by default)
            prediction = 1 if proba >= 0.5 else 0

            # Create result with transaction_id and prediction
            transaction_id = transaction.get("transaction_id")
            if transaction_id is None:
                transaction_id = transaction.get("nameOrig", "unknown")

            result = {
                "transaction_id": transaction_id,
                "prediction": prediction,
                "risk": proba
            }
            results.append(result)

        return results
