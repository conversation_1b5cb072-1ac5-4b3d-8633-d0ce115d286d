# Fraud Detection Platform

A comprehensive full-stack fraud detection and investigation platform with real-time monitoring, case management, and analytics.

## Overview

This platform provides a complete solution for detecting and investigating fraudulent transactions in real-time. It consists of several microservices:

1. **Model Service**: FastAPI service that provides fraud risk scoring
2. **Ingest Service**: Kafka consumer that processes transactions and forwards them to the model service
3. **Dashboard**: React frontend for monitoring transactions and managing fraud cases

## Architecture

The platform follows a microservice architecture:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Ingest Service │────▶│  Model Service  │◀────│    Dashboard    │
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       │
┌─────────────────┐     ┌─────────────────┐              │
│                 │     │                 │              │
│     Kafka       │     │   PostgreSQL    │◀─────────────┘
│                 │     │                 │
└─────────────────┘     └─────────────────┘
```

## Features

- Real-time transaction monitoring
- ML-based fraud detection
- Interactive dashboard with risk heatmap
- Case management system
- Analytics and reporting
- User authentication and authorization
- WebSocket for real-time updates

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Node.js 16+ (for local development)
- Python 3.11+ (for local development)

### Running Locally

1. Clone the repository
2. Start the services using Docker Compose:

```bash
docker-compose up --build
```

3. Access the dashboard at http://localhost:3000
4. Default login credentials:
   - Username: `analyst`
   - Password: `password`

## Development

### Model Service

The model service is a FastAPI application that provides:

- `/health` endpoint for health checks
- `/metrics` endpoint for Prometheus metrics
- `/score` endpoint for scoring transactions

To run it locally:

```bash
cd model-service
pip install -r requirements.txt
uvicorn src.app.main:app --reload
```

### Ingest Service

The ingest service consumes transactions from Kafka, enriches them, and forwards them to the model service. It also provides a WebSocket endpoint for real-time updates.

To run it locally:

```bash
cd ingest-service
pip install -r requirements.txt
uvicorn src.main:app --reload
```

### Dashboard

The dashboard is a React application built with:

- React Router for navigation
- Chart.js for visualizations
- Tailwind CSS for styling

To run it locally:

```bash
cd dashboard
npm install
npm start
```

## Testing

Each service includes unit tests:

```bash
# Model service tests
cd model-service
pytest src/tests/

# Ingest service tests
cd ingest-service
pytest src/tests/
```

## Deployment

The platform can be deployed to Kubernetes using the provided Helm charts:

```bash
helm install fraud-platform ./infra/charts
```

## License

MIT
