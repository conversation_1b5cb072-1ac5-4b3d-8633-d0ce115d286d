"""
Enhanced fraud detection model wrapper that integrates the trained Random Forest model
with backward compatibility for the existing mock model.
"""
import joblib
import pandas as pd
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class EnhancedFraudModel:
    """Enhanced fraud detection model with real ML model integration"""

    def __init__(self, model_path: Optional[str] = None, use_real_model: bool = True):
        """
        Initialize the enhanced model wrapper.

        Args:
            model_path: Path to the trained model file
            use_real_model: Whether to use the real trained model or fallback to mock
        """
        self.use_real_model = use_real_model
        self.real_model = None
        self.feature_columns = [
            'amount', 'oldbalanceOrg', 'newbalanceOrig', 'oldbalanceDest',
            'newbalanceDest', 'merchantFlag', 'type_CASH_OUT', 'type_DEBIT',
            'type_PAYMENT', 'type_TRANSFER'
        ]

        # Try to load the real model
        if use_real_model and model_path:
            try:
                self.real_model = joblib.load(model_path)
                logger.info(f"Successfully loaded trained model from {model_path}")
            except Exception as e:
                logger.warning(f"Could not load real model from {model_path}: {e}")
                logger.info("Falling back to mock model")
                self.use_real_model = False
        else:
            logger.info("Using mock model for demonstration")
            self.use_real_model = False

    def _engineer_features(self, transaction: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply feature engineering to match the trained model's expectations.

        Args:
            transaction: Raw transaction data

        Returns:
            Engineered features dictionary
        """
        features = {}

        # Basic numerical features
        features['amount'] = float(transaction.get('amount', 0))
        features['oldbalanceOrg'] = float(transaction.get('oldbalanceOrg', 0))
        features['newbalanceOrig'] = float(transaction.get('newbalanceOrig', 0))
        features['oldbalanceDest'] = float(transaction.get('oldbalanceDest', 0))
        features['newbalanceDest'] = float(transaction.get('newbalanceDest', 0))

        # Merchant flag (destination starts with 'M')
        dest_name = transaction.get('nameDest', '')
        features['merchantFlag'] = 1 if dest_name.startswith('M') else 0

        # One-hot encode transaction type
        tx_type = transaction.get('type', 'PAYMENT')
        features['type_CASH_OUT'] = 1 if tx_type == 'CASH_OUT' else 0
        features['type_DEBIT'] = 1 if tx_type == 'DEBIT' else 0
        features['type_PAYMENT'] = 1 if tx_type == 'PAYMENT' else 0
        features['type_TRANSFER'] = 1 if tx_type == 'TRANSFER' else 0

        return features

    def _mock_predict(self, transaction: Dict[str, Any]) -> float:
        """
        Mock prediction function for fallback.

        Args:
            transaction: Dictionary containing transaction data

        Returns:
            Mock fraud probability between 0 and 1
        """
        import random

        # Enhanced heuristic-based mock model
        risk_score = 0.05  # Base risk

        # Amount-based risk
        amount = transaction.get('amount', 0)
        if amount > 100000:
            risk_score += 0.4
        elif amount > 50000:
            risk_score += 0.25
        elif amount > 10000:
            risk_score += 0.15

        # Transaction type risk
        tx_type = transaction.get('type', '')
        if tx_type == 'CASH_OUT':
            risk_score += 0.35
        elif tx_type == 'TRANSFER':
            risk_score += 0.25
        elif tx_type == 'DEBIT':
            risk_score += 0.05

        # Balance-based risk indicators
        old_balance = transaction.get('oldbalanceOrg', 0)
        new_balance = transaction.get('newbalanceOrig', 0)

        # Account emptying
        if old_balance > 0 and new_balance == 0:
            risk_score += 0.3

        # Large percentage of balance
        if old_balance > 0 and amount / old_balance > 0.8:
            risk_score += 0.2

        # Merchant flag
        dest_name = transaction.get('nameDest', '')
        if dest_name.startswith('M') and tx_type in ['CASH_OUT', 'TRANSFER']:
            risk_score += 0.1

        # Round number detection
        if amount > 1000 and amount % 1000 == 0:
            risk_score += 0.1

        # Add some randomness
        risk_score += random.uniform(-0.05, 0.05)

        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, risk_score))

    def predict_fraud_probability(self, transactions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Predict fraud probability for a list of transactions.

        Args:
            transactions: List of transaction dictionaries

        Returns:
            List of results with transaction_id and fraud probability
        """
        results = []

        for transaction in transactions:
            try:
                if self.use_real_model and self.real_model is not None:
                    # Use the real trained model
                    features = self._engineer_features(transaction)

                    # Create DataFrame with correct feature order
                    df = pd.DataFrame([features])[self.feature_columns]

                    # Get fraud probability
                    fraud_prob = float(self.real_model.predict_proba(df)[0, 1])

                else:
                    # Use mock model
                    fraud_prob = self._mock_predict(transaction)

                # Create result
                transaction_id = transaction.get("transaction_id")
                if transaction_id is None:
                    transaction_id = transaction.get("nameOrig", "unknown")

                result = {
                    "transaction_id": transaction_id,
                    "risk": fraud_prob,
                    "model_type": "real" if (self.use_real_model and self.real_model) else "mock"
                }

                results.append(result)

            except Exception as e:
                logger.error(f"Error predicting fraud for transaction: {str(e)}")
                # Fallback to mock prediction
                fraud_prob = self._mock_predict(transaction)

                transaction_id = transaction.get("transaction_id",
                                                transaction.get("nameOrig", "unknown"))

                result = {
                    "transaction_id": transaction_id,
                    "risk": fraud_prob,
                    "model_type": "mock_fallback"
                }

                results.append(result)

        return results

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        return {
            "model_type": "real" if (self.use_real_model and self.real_model) else "mock",
            "features": self.feature_columns,
            "model_loaded": self.real_model is not None
        }
