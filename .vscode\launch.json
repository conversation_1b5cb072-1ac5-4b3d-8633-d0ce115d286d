{"version": "0.2.0", "configurations": [{"name": "Model Service", "type": "python", "request": "launch", "program": "${workspaceFolder}/model-service/src/app/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/model-service", "python": "C:\\Users\\<USER>\\anaconda3\\envs\\env\\python.exe", "env": {"PYTHONPATH": "${workspaceFolder}/model-service/src:${workspaceFolder}/model-service", "MODEL_PATH": "${workspaceFolder}/model-service/models/fraud_detection_model.pkl"}, "args": []}, {"name": "Ingest Service", "type": "python", "request": "launch", "program": "${workspaceFolder}/ingest-service/src/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/ingest-service", "python": "C:\\Users\\<USER>\\anaconda3\\envs\\env\\python.exe", "env": {"PYTHONPATH": "${workspaceFolder}/ingest-service/src:${workspaceFolder}/ingest-service"}, "args": []}]}