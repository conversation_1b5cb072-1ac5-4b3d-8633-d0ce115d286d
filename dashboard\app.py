"""
Simple Flask-based dashboard for the fraud detection platform.
"""
import requests
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify

app = Flask(__name__)
app.config['SECRET_KEY'] = 'fraud-detection-secret'

# Configuration
MODEL_SERVICE_URL = "http://localhost:8000"
INGEST_SERVICE_URL = "http://localhost:9000"

# In-memory storage for demo purposes
transactions = []
alerts = []

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/health')
def health():
    """Health check endpoint"""
    try:
        model_health = requests.get(f"{MODEL_SERVICE_URL}/health", timeout=5)
        ingest_health = requests.get(f"{INGEST_SERVICE_URL}/health", timeout=5)

        return jsonify({
            "status": "ok",
            "services": {
                "model": model_health.status_code == 200,
                "ingest": ingest_health.status_code == 200
            },
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/transactions', methods=['GET'])
def get_transactions():
    """Get recent transactions"""
    return jsonify(transactions[-100:])  # Return last 100 transactions

@app.route('/api/transactions', methods=['POST'])
def submit_transaction():
    """Submit a new transaction for processing"""
    try:
        transaction_data = request.json

        # Send to ingest service
        response = requests.post(
            f"{INGEST_SERVICE_URL}/process",
            json=transaction_data,
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()

            # Store transaction with result
            transaction_with_result = transaction_data.copy()
            transaction_with_result.update({
                'risk_score': result['risk_score'],
                'timestamp': result['timestamp'],
                'status': 'processed'
            })
            transactions.append(transaction_with_result)

            # Check if high risk
            if result['risk_score'] >= 0.8:
                alert = {
                    'transaction_id': result['transaction_id'],
                    'risk_score': result['risk_score'],
                    'amount': transaction_data.get('amount', 0),
                    'type': transaction_data.get('type', 'Unknown'),
                    'timestamp': result['timestamp']
                }
                alerts.append(alert)

            return jsonify(result)
        else:
            return jsonify({"error": "Failed to process transaction"}), 500

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/alerts')
def get_alerts():
    """Get recent high-risk alerts"""
    return jsonify(alerts[-50:])  # Return last 50 alerts

@app.route('/api/stats')
def get_stats():
    """Get dashboard statistics"""
    total_transactions = len(transactions)
    high_risk = len([t for t in transactions if t.get('risk_score', 0) >= 0.8])
    medium_risk = len([t for t in transactions if 0.5 <= t.get('risk_score', 0) < 0.8])
    low_risk = total_transactions - high_risk - medium_risk

    return jsonify({
        'total_transactions': total_transactions,
        'high_risk': high_risk,
        'medium_risk': medium_risk,
        'low_risk': low_risk,
        'total_alerts': len(alerts)
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000, debug=True)
