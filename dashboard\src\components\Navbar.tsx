import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../services/AuthContext';
import '../styles/Navbar.css';

const Navbar = () => {
  const { logout, user } = useAuth();
  const location = useLocation();

  const handleLogout = () => {
    logout();
  };

  return (
    <nav className="navbar">
      <div className="navbar-brand">
        <h1>Fraud Detection</h1>
      </div>
      <div className="navbar-menu">
        <Link 
          to="/" 
          className={`navbar-item ${location.pathname === '/' ? 'active' : ''}`}
        >
          Dashboard
        </Link>
        <Link 
          to="/cases" 
          className={`navbar-item ${location.pathname === '/cases' ? 'active' : ''}`}
        >
          Case Management
        </Link>
        <Link 
          to="/analytics" 
          className={`navbar-item ${location.pathname === '/analytics' ? 'active' : ''}`}
        >
          Analytics
        </Link>
      </div>
      <div className="navbar-user">
        {user && (
          <>
            <span className="user-info">
              <span className="username">{user.username}</span>
              <span className="role">{user.role}</span>
            </span>
            <button className="logout-button" onClick={handleLogout}>
              Logout
            </button>
          </>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
