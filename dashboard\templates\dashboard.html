<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fraud Detection Dashboard</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .high-risk { color: #e74c3c; }
        .medium-risk { color: #f39c12; }
        .low-risk { color: #27ae60; }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .panel-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
        }

        .panel-content {
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .transaction-form {
            display: grid;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn:hover {
            background: #0056b3;
        }

        .transaction-item, .alert-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .transaction-item:last-child, .alert-item:last-child {
            border-bottom: none;
        }

        .risk-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .risk-high { background: #ffe6e6; color: #c62828; }
        .risk-medium { background: #fff3e0; color: #ef6c00; }
        .risk-low { background: #e8f5e8; color: #2e7d32; }

        .status {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Fraud Detection Dashboard</h1>
    </div>

    <div class="status" id="status">Connecting...</div>

    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="total-transactions">0</div>
                <div class="stat-label">Total Transactions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value high-risk" id="high-risk">0</div>
                <div class="stat-label">High Risk</div>
            </div>
            <div class="stat-card">
                <div class="stat-value medium-risk" id="medium-risk">0</div>
                <div class="stat-label">Medium Risk</div>
            </div>
            <div class="stat-card">
                <div class="stat-value low-risk" id="low-risk">0</div>
                <div class="stat-label">Low Risk</div>
            </div>
        </div>

        <div class="content-grid">
            <div class="panel">
                <div class="panel-header">Submit Transaction</div>
                <div class="panel-content">
                    <form class="transaction-form" id="transaction-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="type">Transaction Type</label>
                                <select id="type" name="type" required>
                                    <option value="PAYMENT">Payment</option>
                                    <option value="TRANSFER">Transfer</option>
                                    <option value="CASH_OUT">Cash Out</option>
                                    <option value="DEBIT">Debit</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="amount">Amount ($)</label>
                                <input type="number" id="amount" name="amount" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="nameOrig">Origin Account</label>
                                <input type="text" id="nameOrig" name="nameOrig" placeholder="C123456789" required>
                            </div>
                            <div class="form-group">
                                <label for="nameDest">Destination Account</label>
                                <input type="text" id="nameDest" name="nameDest" placeholder="M987654321" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="oldbalanceOrg">Origin Old Balance ($)</label>
                                <input type="number" id="oldbalanceOrg" name="oldbalanceOrg" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="newbalanceOrig">Origin New Balance ($)</label>
                                <input type="number" id="newbalanceOrig" name="newbalanceOrig" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="oldbalanceDest">Destination Old Balance ($)</label>
                                <input type="number" id="oldbalanceDest" name="oldbalanceDest" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="newbalanceDest">Destination New Balance ($)</label>
                                <input type="number" id="newbalanceDest" name="newbalanceDest" step="0.01" min="0" required>
                            </div>
                        </div>
                        <button type="submit" class="btn">Process Transaction</button>
                    </form>
                </div>
            </div>

            <div class="panel">
                <div class="panel-header">Recent Transactions</div>
                <div class="panel-content" id="transactions-list">
                    <p>No transactions yet. Submit a transaction to get started.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Status indicator
        const statusEl = document.getElementById('status');
        statusEl.textContent = 'Ready';
        statusEl.className = 'status connected';

        // Form submission
        document.getElementById('transaction-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const transaction = {
                step: 1,
                type: formData.get('type'),
                amount: parseFloat(formData.get('amount')),
                nameOrig: formData.get('nameOrig'),
                oldbalanceOrg: parseFloat(formData.get('oldbalanceOrg')),
                newbalanceOrig: parseFloat(formData.get('newbalanceOrig')),
                nameDest: formData.get('nameDest'),
                oldbalanceDest: parseFloat(formData.get('oldbalanceDest')),
                newbalanceDest: parseFloat(formData.get('newbalanceDest'))
            };

            try {
                const response = await fetch('/api/transactions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(transaction)
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('Transaction processed:', result);
                    e.target.reset();

                    // Refresh data
                    await loadTransactions();
                    await updateStats();

                    // Show result
                    alert(`Transaction processed!\nRisk Score: ${(result.risk_score * 100).toFixed(1)}%`);
                } else {
                    alert('Failed to process transaction');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error processing transaction');
            }
        });

        function addTransactionToList(transaction) {
            const list = document.getElementById('transactions-list');

            if (list.children.length === 1 && list.children[0].tagName === 'P') {
                list.innerHTML = '';
            }

            const riskClass = transaction.risk_score >= 0.8 ? 'risk-high' :
                             transaction.risk_score >= 0.5 ? 'risk-medium' : 'risk-low';

            const item = document.createElement('div');
            item.className = 'transaction-item';
            item.innerHTML = `
                <div>
                    <strong>${transaction.nameOrig}</strong> → ${transaction.nameDest}<br>
                    <small>${transaction.type} • $${transaction.amount.toLocaleString()}</small>
                </div>
                <div class="risk-badge ${riskClass}">
                    ${(transaction.risk_score * 100).toFixed(1)}%
                </div>
            `;

            list.insertBefore(item, list.firstChild);

            // Keep only last 10 transactions visible
            while (list.children.length > 10) {
                list.removeChild(list.lastChild);
            }
        }

        function showAlert(alert) {
            // Simple alert for high-risk transactions
            const message = `🚨 HIGH RISK ALERT!\nTransaction: ${alert.transaction_id}\nRisk: ${(alert.risk_score * 100).toFixed(1)}%\nAmount: $${alert.amount.toLocaleString()}`;

            // You could replace this with a more sophisticated notification system
            if (confirm(message + '\n\nClick OK to acknowledge this alert.')) {
                console.log('Alert acknowledged');
            }
        }

        async function updateStats() {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();

                document.getElementById('total-transactions').textContent = stats.total_transactions;
                document.getElementById('high-risk').textContent = stats.high_risk;
                document.getElementById('medium-risk').textContent = stats.medium_risk;
                document.getElementById('low-risk').textContent = stats.low_risk;
            } catch (error) {
                console.error('Error updating stats:', error);
            }
        }

        async function loadTransactions() {
            try {
                const response = await fetch('/api/transactions');
                const transactions = await response.json();

                const list = document.getElementById('transactions-list');
                list.innerHTML = '';

                if (transactions.length === 0) {
                    list.innerHTML = '<p>No transactions yet. Submit a transaction to get started.</p>';
                } else {
                    transactions.slice(-10).reverse().forEach(transaction => {
                        addTransactionToList(transaction);
                    });
                }
            } catch (error) {
                console.error('Error loading transactions:', error);
            }
        }

        // Load initial data
        updateStats();
        loadTransactions();
    </script>
</body>
</html>
