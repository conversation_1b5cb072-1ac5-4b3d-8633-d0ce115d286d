/* Analytics page styles */
.analytics-container {
  padding: 1rem;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.date-range-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-range-selector select {
  width: auto;
}

.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.analytics-row {
  width: 100%;
}

.chart-container {
  height: 300px;
  position: relative;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 0.5rem;
}

.stat-item h3 {
  font-size: 0.875rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.stat-item p {
  font-size: 1.5rem;
  font-weight: 600;
}
