"""
Test cases for ingest service.
"""
import pytest
from unittest.mock import patch, MagicMock
import sys
from pathlib import Path
from fastapi.testclient import TestClient

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from main import app

client = TestClient(app)

def test_health_endpoint():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert "version" in data
    assert "timestamp" in data

def test_metrics_endpoint():
    """Test metrics endpoint"""
    response = client.get("/metrics")
    assert response.status_code == 200
    assert b"ingest_messages_processed_total" in response.content

@patch('main.enrich_transaction')
@patch('requests.post')
def test_process_message(mock_post, mock_enrich):
    """Test message processing logic"""
    # Mock enrichment function
    mock_enrich.return_value = {
        "transaction_id": "C111",
        "step": 1,
        "type": "PAYMENT",
        "amount": 1000.0,
        "nameOrig": "C111",
        "oldbalanceOrg": 5000.0,
        "newbalanceOrig": 4000.0,
        "nameDest": "M111",
        "oldbalanceDest": 0.0,
        "newbalanceDest": 0.0,
        "merchantFlag": 1,
        "type_CASH_OUT": 0,
        "type_DEBIT": 0,
        "type_PAYMENT": 1,
        "type_TRANSFER": 0,
        "balanceDiffOrig": 1000.0,
        "balanceDiffDest": 0.0
    }

    # Mock model service response
    mock_post.return_value = MagicMock()
    mock_post.return_value.status_code = 200
    mock_post.return_value.json.return_value = {
        "results": [
            {
                "transaction_id": "C111",
                "risk": 0.1
            }
        ]
    }

    # Create test transaction
    transaction = {
        "step": 1,
        "type": "PAYMENT",
        "amount": 1000.0,
        "nameOrig": "C111",
        "oldbalanceOrg": 5000.0,
        "newbalanceOrig": 4000.0,
        "nameDest": "M111",
        "oldbalanceDest": 0.0,
        "newbalanceDest": 0.0
    }

    # Call the enrichment function directly to test
    from utils import enrich_transaction
    enriched = enrich_transaction(transaction)

    # Verify enrichment adds expected fields
    assert "merchantFlag" in enriched
    assert "type_PAYMENT" in enriched
    assert enriched["merchantFlag"] == 1  # M prefix indicates merchant
    assert enriched["type_PAYMENT"] == 1

    # Verify balance difference calculation
    assert "balanceDiffOrig" in enriched
    assert enriched["balanceDiffOrig"] == 1000.0
