import React, { useState } from 'react';
import '../styles/CaseForm.css';

const CaseForm = ({ initialCase, onSubmit }) => {
  const [formData, setFormData] = useState({
    transaction_id: initialCase?.transaction_id || '',
    tag: initialCase?.tag || 'NEEDS_REVIEW',
    comment: initialCase?.comment || '',
    status: initialCase?.status || 'open'
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="case-form-wrapper">
      <h2>{initialCase ? 'Update Case' : 'Create New Case'}</h2>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="transaction_id">Transaction ID:</label>
          <input
            type="text"
            id="transaction_id"
            name="transaction_id"
            value={formData.transaction_id}
            onChange={handleChange}
            required
            disabled={!!initialCase}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="tag">Tag:</label>
          <select
            id="tag"
            name="tag"
            value={formData.tag}
            onChange={handleChange}
            required
          >
            <option value="NEEDS_REVIEW">Needs Review</option>
            <option value="SUSPICIOUS">Suspicious</option>
            <option value="CONFIRMED">Confirmed Fraud</option>
            <option value="FP">False Positive</option>
          </select>
        </div>
        
        <div className="form-group">
          <label htmlFor="status">Status:</label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            required
          >
            <option value="open">Open</option>
            <option value="pending">Pending</option>
            <option value="closed">Closed</option>
          </select>
        </div>
        
        <div className="form-group">
          <label htmlFor="comment">Comment:</label>
          <textarea
            id="comment"
            name="comment"
            value={formData.comment}
            onChange={handleChange}
            rows={4}
          ></textarea>
        </div>
        
        <button
          type="submit"
          className="submit-button"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Submitting...' : initialCase ? 'Update' : 'Create'}
        </button>
      </form>
    </div>
  );
};

export default CaseForm;
