import React, { useState, useEffect } from 'react';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { Pie } from 'react-chartjs-2';
import '../styles/RiskHeatmap.css';

// Register ChartJS components
ChartJS.register(ArcE<PERSON>, Tooltip, Legend);

const RiskHeatmap = ({ transactions }) => {
  const [heatmapData, setHeatmapData] = useState({
    hourly: Array(24).fill(0).map(() => ({ total: 0, high: 0, medium: 0, low: 0 })),
    daily: Array(7).fill(0).map(() => ({ total: 0, high: 0, medium: 0, low: 0 }))
  });
  
  const [view, setView] = useState('hourly');
  
  useEffect(() => {
    if (!transactions || transactions.length === 0) return;
    
    const hourlyData = Array(24).fill(0).map(() => ({ total: 0, high: 0, medium: 0, low: 0 }));
    const dailyData = Array(7).fill(0).map(() => ({ total: 0, high: 0, medium: 0, low: 0 }));
    
    transactions.forEach(transaction => {
      if (!transaction.timestamp) return;
      
      const date = new Date(transaction.timestamp);
      const hour = date.getHours();
      const day = date.getDay();
      
      // Update hourly data
      hourlyData[hour].total += 1;
      if (transaction.risk_score >= 0.8) {
        hourlyData[hour].high += 1;
      } else if (transaction.risk_score >= 0.5) {
        hourlyData[hour].medium += 1;
      } else {
        hourlyData[hour].low += 1;
      }
      
      // Update daily data
      dailyData[day].total += 1;
      if (transaction.risk_score >= 0.8) {
        dailyData[day].high += 1;
      } else if (transaction.risk_score >= 0.5) {
        dailyData[day].medium += 1;
      } else {
        dailyData[day].low += 1;
      }
    });
    
    setHeatmapData({ hourly: hourlyData, daily: dailyData });
  }, [transactions]);
  
  const getChartData = () => {
    const data = view === 'hourly' ? heatmapData.hourly : heatmapData.daily;
    
    return {
      labels: ['High Risk', 'Medium Risk', 'Low Risk'],
      datasets: [
        {
          data: [
            data.reduce((sum, item) => sum + item.high, 0),
            data.reduce((sum, item) => sum + item.medium, 0),
            data.reduce((sum, item) => sum + item.low, 0)
          ],
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };
  
  const renderHeatmap = () => {
    const data = view === 'hourly' ? heatmapData.hourly : heatmapData.daily;
    const labels = view === 'hourly' 
      ? Array(24).fill(0).map((_, i) => `${i}:00`)
      : ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    return (
      <div className="heatmap-grid">
        {data.map((item, index) => {
          const total = item.total || 1; // Avoid division by zero
          const highPercent = (item.high / total) * 100;
          const mediumPercent = (item.medium / total) * 100;
          const lowPercent = (item.low / total) * 100;
          
          // Calculate intensity for cell color
          const intensity = Math.min(item.total / 5, 1); // Normalize by 5 transactions
          
          return (
            <div 
              key={index} 
              className="heatmap-cell"
              style={{
                backgroundColor: `rgba(0, 0, 0, ${intensity * 0.1})`,
                borderColor: item.high > 0 ? 'rgba(255, 99, 132, 0.5)' : 'transparent'
              }}
            >
              <div className="cell-label">{labels[index]}</div>
              <div className="cell-count">{item.total}</div>
              {item.total > 0 && (
                <div className="risk-bars">
                  <div 
                    className="risk-bar high" 
                    style={{ width: `${highPercent}%` }}
                    title={`High Risk: ${item.high}`}
                  ></div>
                  <div 
                    className="risk-bar medium" 
                    style={{ width: `${mediumPercent}%` }}
                    title={`Medium Risk: ${item.medium}`}
                  ></div>
                  <div 
                    className="risk-bar low" 
                    style={{ width: `${lowPercent}%` }}
                    title={`Low Risk: ${item.low}`}
                  ></div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };
  
  return (
    <div className="risk-heatmap-container">
      <div className="heatmap-controls">
        <button 
          className={`view-button ${view === 'hourly' ? 'active' : ''}`}
          onClick={() => setView('hourly')}
        >
          By Hour
        </button>
        <button 
          className={`view-button ${view === 'daily' ? 'active' : ''}`}
          onClick={() => setView('daily')}
        >
          By Day
        </button>
      </div>
      
      <div className="heatmap-content">
        <div className="heatmap-chart">
          {renderHeatmap()}
        </div>
        
        <div className="risk-distribution">
          <h3>Risk Distribution</h3>
          <div className="pie-chart-container">
            <Pie data={getChartData()} options={{ maintainAspectRatio: false }} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RiskHeatmap;
